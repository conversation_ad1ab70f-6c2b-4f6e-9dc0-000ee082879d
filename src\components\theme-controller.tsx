'use client'

import { useEffect, useRef } from 'react'
import { useThemeStore, type Theme } from '@/stores/theme-store'

interface ThemeOption {
  value: Theme
  label: string
  icon: string
  description: string
}

const themeOptions: ThemeOption[] = [
  {
    value: 'light',
    label: 'Light',
    icon: 'icon-[solar--sun-2-bold-duotone]',
    description: 'Light theme'
  },
  {
    value: 'dark',
    label: 'Dark',
    icon: 'icon-[solar--moon-bold-duotone]',
    description: 'Dark theme'
  },
  {
    value: 'system',
    label: 'System',
    icon: 'icon-[solar--monitor-bold-duotone]',
    description: 'Follow system preference'
  }
]

interface ThemeControllerProps {
  className?: string
  buttonClassName?: string
  dropdownClassName?: string
}

export function ThemeController({ 
  className = '',
  buttonClassName = '',
  dropdownClassName = ''
}: ThemeControllerProps) {
  const { theme, resolvedTheme, setTheme, initializeTheme } = useThemeStore()
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLDivElement>(null)

  // Initialize theme on mount
  useEffect(() => {
    initializeTheme()
  }, [initializeTheme])

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      // Close dropdown
      if (buttonRef.current) {
        buttonRef.current.blur()
      }
    }
  }

  // Handle theme selection
  const handleThemeSelect = (selectedTheme: Theme) => {
    setTheme(selectedTheme)
    // Close dropdown by removing focus
    if (buttonRef.current) {
      buttonRef.current.blur()
    }
  }

  // Get current theme icon
  const getCurrentIcon = () => {
    if (theme === 'system') {
      return resolvedTheme === 'dark' 
        ? 'icon-[solar--moon-bold-duotone]' 
        : 'icon-[solar--sun-2-bold-duotone]'
    }
    return themeOptions.find(option => option.value === theme)?.icon || 'icon-[solar--sun-2-bold-duotone]'
  }

  return (
    <div 
      className={`dropdown dropdown-end ${className}`}
      ref={dropdownRef}
      onKeyDown={handleKeyDown}
    >
      <div
        ref={buttonRef}
        tabIndex={0}
        role="button"
        className={`btn btn-circle btn-outline border-base-300 ${buttonClassName}`}
        aria-label={`Current theme: ${theme}. Click to change theme`}
        aria-haspopup="menu"
        aria-expanded="false"
      >
        <span className={getCurrentIcon()} aria-hidden="true"></span>
      </div>
      
      <ul 
        tabIndex={0} 
        className={`dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-lg border border-base-300 ${dropdownClassName}`}
        role="menu"
        aria-label="Theme selection menu"
      >
        {themeOptions.map((option) => (
          <li key={option.value} role="none">
            <button
              type="button"
              className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-colors hover:bg-base-200 focus:bg-base-200 ${
                theme === option.value ? 'bg-primary text-primary-content' : ''
              }`}
              onClick={() => handleThemeSelect(option.value)}
              role="menuitem"
              aria-label={option.description}
            >
              <span 
                className={`${option.icon} text-lg`} 
                aria-hidden="true"
              ></span>
              <div className="flex flex-col items-start">
                <span className="font-medium">{option.label}</span>
                <span className="text-xs opacity-70">{option.description}</span>
              </div>
              {theme === option.value && (
                <span 
                  className="icon-[solar--check-circle-bold] text-lg ml-auto" 
                  aria-hidden="true"
                ></span>
              )}
            </button>
          </li>
        ))}
      </ul>
    </div>
  )
}
