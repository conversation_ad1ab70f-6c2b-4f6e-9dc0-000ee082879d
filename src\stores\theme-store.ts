import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type Theme = 'light' | 'dark' | 'system'

interface ThemeState {
  theme: Theme
  resolvedTheme: 'light' | 'dark'
  setTheme: (theme: Theme) => void
  initializeTheme: () => void
}

const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light'
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

const resolveTheme = (theme: Theme): 'light' | 'dark' => {
  if (theme === 'system') {
    return getSystemTheme()
  }
  return theme
}

const applyTheme = (resolvedTheme: 'light' | 'dark') => {
  if (typeof document === 'undefined') return
  
  const root = document.documentElement
  const themeToApply = resolvedTheme === 'dark' ? 'material-dark' : 'material-light'
  
  root.setAttribute('data-theme', themeToApply)
  root.style.colorScheme = resolvedTheme
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      theme: 'system',
      resolvedTheme: 'light',
      
      setTheme: (theme: Theme) => {
        const resolvedTheme = resolveTheme(theme)
        applyTheme(resolvedTheme)
        set({ theme, resolvedTheme })
      },
      
      initializeTheme: () => {
        const { theme } = get()
        const resolvedTheme = resolveTheme(theme)
        applyTheme(resolvedTheme)
        set({ resolvedTheme })
        
        // Listen for system theme changes when theme is set to 'system'
        if (typeof window !== 'undefined') {
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
          const handleChange = () => {
            const currentTheme = get().theme
            if (currentTheme === 'system') {
              const newResolvedTheme = getSystemTheme()
              applyTheme(newResolvedTheme)
              set({ resolvedTheme: newResolvedTheme })
            }
          }
          
          mediaQuery.addEventListener('change', handleChange)
          
          // Cleanup function (though we don't return it here, it's handled by the component)
          return () => mediaQuery.removeEventListener('change', handleChange)
        }
      },
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({ theme: state.theme }),
    }
  )
)
